import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const AgentsEndpoints = {
  post: '/auth/tools/callback-url-oauth',
  get: '/auth/tools',
};

// Define the Category interface

// Define filters interface for agents API

// Define the API response structure

// Create a hook to use the Agentss API
export const useAuthToolsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get a single Agents by ID
  const useGetAuthTools = (id: string) => {
    return apiServices.useGetItemService<any>({
      url: AgentsEndpoints.get,
      id,
      queryOptions: {
        enabled: id?.length > 0,
      },
    });
  };

  // Create a new Agents
  const useCreateAuthTools = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<any, any>({
      url: AgentsEndpoints.post,
      onSuccess,
    });
  };

  return {
    useGetAuthTools,
    useCreateAuthTools,
  };
};
