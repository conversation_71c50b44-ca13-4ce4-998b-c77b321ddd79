import { paths } from 'src/routes/paths';

import axios from 'src/utils/axios';

import { STORAGE_KEY } from './constant';

// ----------------------------------------------------------------------

export function jwtDecode(token: string) {
  try {
    if (!token) return null;

    const parts = token.split('.');
    if (parts.length < 2) {
      throw new Error('Invalid token!');
    }

    const base64Url = parts[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const decoded = JSON.parse(atob(base64));

    return decoded;
  } catch (error) {
    console.error('Error decoding token:', error);
    throw error;
  }
}

// ----------------------------------------------------------------------

export function isValidToken(accessToken: string) {
  if (!accessToken) {
    return false;
  }

  try {
    const decoded = jwtDecode(accessToken);

    if (!decoded || !('exp' in decoded)) {
      return false;
    }

    const currentTime = Date.now() / 1000;

    return decoded.exp > currentTime;
  } catch (error) {
    console.error('Error during token validation:', error);
    return false;
  }
}

// ----------------------------------------------------------------------

export function tokenExpired(exp: number) {
  const currentTime = Date.now();
  const timeLeft = exp * 1000 - currentTime;

  // Only set timeout if token hasn't expired yet and has reasonable time left
  if (timeLeft > 0) {
    setTimeout(() => {
      try {
        alert('Token expired!');
        sessionStorage.removeItem(STORAGE_KEY);
        localStorage.removeItem(STORAGE_KEY);
        window.location.href = paths.auth.jwt.signIn;
      } catch (error) {
        console.error('Error during token expiration:', error);
        throw error;
      }
    }, timeLeft);
  } else {
    // Token is already expired, handle immediately but don't show alert during sign-in
    console.warn('Token is already expired');
  }
}

// ----------------------------------------------------------------------

export async function setSession(accessToken: string | null, rememberMe: boolean = true) {
  try {
    if (accessToken) {
      // Validate token before setting session
      if (!isValidToken(accessToken)) {
        throw new Error('Token is expired or invalid!');
      }

      // Always set in sessionStorage for current session
      sessionStorage.setItem(STORAGE_KEY, accessToken);

      // Set in localStorage only if rememberMe is true for persistent login
      if (rememberMe) {
        localStorage.setItem(STORAGE_KEY, accessToken);
      } else {
        localStorage.removeItem(STORAGE_KEY);
      }

      axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;

      // Set up token expiration handling
      const decodedToken = jwtDecode(accessToken);
      if (decodedToken && 'exp' in decodedToken) {
        tokenExpired(decodedToken.exp);
      }
    } else {
      // Clear all stored tokens
      sessionStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(STORAGE_KEY);
      delete axios.defaults.headers.common.Authorization;
    }
  } catch (error) {
    console.error('Error during set session:', error);
    // Clear tokens on error
    sessionStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(STORAGE_KEY);
    delete axios.defaults.headers.common.Authorization;
    throw error;
  }
}

// ----------------------------------------------------------------------

/**
 * Get stored token from sessionStorage or localStorage
 * Prioritizes sessionStorage over localStorage
 */
export function getStoredToken(): string | null {
  try {
    // First check sessionStorage (current session)
    let token = sessionStorage.getItem(STORAGE_KEY);

    // If not found, check localStorage (persistent login)
    if (!token) {
      token = localStorage.getItem(STORAGE_KEY);

      // If found in localStorage, also set in sessionStorage for current session
      if (token && isValidToken(token)) {
        sessionStorage.setItem(STORAGE_KEY, token);
      }
    }

    return token;
  } catch (error) {
    console.error('Error getting stored token:', error);
    return null;
  }
}

// ----------------------------------------------------------------------

/**
 * Clear all authentication data
 */
export function clearAuthData(): void {
  try {
    sessionStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(STORAGE_KEY);
    delete axios.defaults.headers.common.Authorization;
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
}
