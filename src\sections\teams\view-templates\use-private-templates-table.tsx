import { Icon } from '@iconify/react';
import { Avatar, Box, Chip, IconButton, Typography } from '@mui/material';
import { useState } from 'react';
import { useTable } from 'src/components/table';
import { AppTablePropsType } from 'src/components/table/app-table/types/app-table';
import { TeamTeamplatesType } from 'src/services/api/use-teams-api';
import { fDate } from 'src/utils/format-time';

export const usePrivateTemplatesTable = (privateTemplates: TeamTeamplatesType[]) => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  const table = useTable();

  // Handle file selection
  const handleSelectFile = (id: string) => {
    setSelectedFiles((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleSelectAllFiles = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(privateTemplates.map((template) => template.id.toString()));
    } else {
      setSelectedFiles([]);
    }
  };

  // Table head labels
  const headLabels = [
    { id: 'name', label: 'Name' },
    { id: 'dateCreated', label: 'Date Created', align: 'center' },
    { id: 'type', label: 'Type', align: 'center' },
    { id: 'category', label: 'Category', align: 'center' },
    { id: 'tools', label: 'Tools', align: 'center' },
    { id: 'llmModel', label: 'LLM Model', align: 'center' },
    { id: 'status', label: 'Status', align: 'center' },
    { id: 'action', label: 'Action', align: 'center' },
  ];

  // Table columns configuration
  const columns: AppTablePropsType<TeamTeamplatesType>['columns'] = [
    {
      name: 'name',
      PreviewComponent: ({ name }) => (
        <Box display="flex" alignItems="center" gap={1.5}>
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: 'text.primary',
              color: 'background.paper',
              fontSize: '0.875rem',
              fontWeight: 600,
            }}
          />
          <Typography variant="body2" fontWeight={500} color="text.primary">
            {name}
          </Typography>
        </Box>
      ),
    },
    {
      name: 'createdAt',
      PreviewComponent: ({ createdAt }) => (
        <Typography variant="body2" color="text.secondary">
          {fDate(createdAt)}
        </Typography>
      ),
    },
    {
      name: 'type',
      PreviewComponent: ({ type }) => (
        <Typography variant="body2" color="text.secondary">
          {type}
        </Typography>
      ),
    },
    {
      name: 'category',
      PreviewComponent: ({ category }) => (
        <Typography variant="body2" color="text.secondary">
          {category.name}
        </Typography>
      ),
    },
    {
      name: 'category.id',
      PreviewComponent: (data) => (
        <Box display="flex" alignItems="center" justifyContent="center">
          <Icon icon="mdi:tools" width={16} height={16} style={{ color: '#D32F2F' }} />
        </Box>
      ),
    },
    {
      name: 'model',
      PreviewComponent: ({ model }) => (
        <Typography variant="body2" color="text.secondary">
          {model.replace(/_/g, ' ')}
        </Typography>
      ),
    },
    {
      name: 'status',
      PreviewComponent: (
        { visibility } // Destructure visibility directly from props
      ) => (
        <Chip
          label={visibility}
          size="small"
          sx={{
            bgcolor: visibility === 'PRIVATE' ? '#FFEBEE' : '#E8F5E8',
            color: visibility === 'PRIVATE' ? '#D32F2F' : '#2E7D32',
            fontWeight: 500,
            fontSize: '0.75rem',
            height: '24px',
            '& .MuiChip-label': {
              px: 1.5,
            },
          }}
        />
      ),
    },
    {
      name: 'status',
      cellSx: { width: '50px' },
      PreviewComponent: () => (
        <IconButton
          size="small"
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <Icon icon="eva:more-vertical-fill" width={20} height={20} />
        </IconButton>
      ),
    },
  ];

  // Select configuration
  const selectConfig = {
    idPath: 'id' as keyof TeamTeamplatesType,
    handleSelectRow: (row: TeamTeamplatesType) => handleSelectFile(row.id.toString()),
    handleSelectAllRows: (ids: string[]) => (checked: boolean) => handleSelectAllFiles(checked),
    selected: selectedFiles,
    rowCount: privateTemplates.length,
    numSelected: selectedFiles.length,
    selectedRowsActions: [],
  };

  return {
    privateTemplates,
    selectedFiles,
    handleSelectFile,
    handleSelectAllFiles,
    table,
    columns,
    headLabels,
    selectConfig,
  };
};
