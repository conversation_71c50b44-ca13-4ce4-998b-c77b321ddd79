import {
  Alert,
  Box,
  Paper,
  Typography,
  Chip,
  Card,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Avatar,
  Stack,
  Divider,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { enqueueSnackbar } from 'notistack';
import { Iconify } from 'src/components/iconify';
import { StreamedMessage } from './use-agents-chat';

type ChatMessagesType = {
  chatMessages: StreamedMessage[];
  message?: string;
};

const ChatMessagesEvents = ({ chatMessages, message }: ChatMessagesType) => {
  const currentLng = localStorage.getItem('i18nextLng') || 'en';
  const errorMsg = chatMessages?.find((msg) => msg.event === 'error');
  const [expandedAccordion, setExpandedAccordion] = useState<number | null>(null);

  const handleAccordionChange = (messageId: number) => {
    setExpandedAccordion(expandedAccordion === messageId ? null : messageId);
  };

  useEffect(() => {
    if (errorMsg) {
      enqueueSnackbar({
        variant: 'error',
        message: errorMsg?.message,
        anchorOrigin: {
          vertical: 'top',
          horizontal: currentLng === 'en' ? 'right' : 'left',
        },
      });
    }
  }, [chatMessages]);

  console.log('Message', message);

  // Group messages by conversation flow
  const groupedMessages = chatMessages.reduce(
    (acc, msg, index) => {
      if (msg.source === 'user') {
        // Start a new conversation group
        acc.push({
          userMessage: msg,
          responses: [],
          id: msg.id,
        });
      } else if (acc.length > 0) {
        // Add to the latest conversation group
        acc[acc.length - 1].responses.push(msg);
      }
      return acc;
    },
    [] as Array<{ userMessage: StreamedMessage; responses: StreamedMessage[]; id: number }>
  );

  return (
    <>
      {message && message.length > 0 && (
        <Alert sx={{ mt: '20px' }} variant="filled" color="error">
          <Typography sx={{ color: 'white' }}>{message}</Typography>
        </Alert>
      )}

      <Stack spacing={3} sx={{ mt: 2 }}>
        {groupedMessages.length === 0 && chatMessages.length > 0 && (
          // Handle messages that don't start with user message
          <Stack spacing={2}>
            {chatMessages.map((msg) => (
              <Accordion
                key={msg.id}
                expanded={expandedAccordion === msg.id}
                onChange={() => handleAccordionChange(msg.id)}
                sx={{
                  boxShadow: 'none',
                  border: '1px solid #E0E0E0',
                  borderRadius: '12px !important',
                  backgroundColor: '#F8F9FA',
                  '&:before': {
                    display: 'none',
                  },
                  '&.Mui-expanded': {
                    margin: 0,
                  },
                }}
              >
                <AccordionSummary
                  expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                  sx={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    minHeight: '48px',
                    '&.Mui-expanded': {
                      borderBottomLeftRadius: 0,
                      borderBottomRightRadius: 0,
                    },
                    '& .MuiAccordionSummary-content': {
                      alignItems: 'center',
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        backgroundColor: '#9C6FE4',
                        color: 'white',
                        fontSize: '14px',
                        fontWeight: 'bold',
                      }}
                    >
                      A
                    </Avatar>
                    <Typography variant="body2" fontWeight={500}>
                      {msg.event === 'task_result'
                        ? 'Task Result'
                        : msg.event === 'tool_call_request'
                          ? 'Tool Request'
                          : msg.event === 'tool_call_response'
                            ? 'Tool Response'
                            : 'Agent Response'}
                    </Typography>
                    {msg.status === 'completed' && (
                      <Chip size="small" color="success" label="Completed" />
                    )}
                    {msg.isError && <Chip size="small" color="error" label="Failed" />}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" whiteSpace="pre-wrap">
                    {msg.message}
                  </Typography>
                </AccordionDetails>
              </Accordion>
            ))}
          </Stack>
        )}
        {groupedMessages.map((group) => (
          <Box key={group.id}>
            {/* User Message - Right aligned, 6 columns width */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
              <Box sx={{ width: '50%', maxWidth: '600px' }}>
                <Paper
                  sx={{
                    p: 2.5,
                    backgroundColor: '#9C6FE4',
                    color: 'white',
                    borderRadius: '18px 18px 4px 18px',
                    boxShadow: '0 2px 8px rgba(156, 111, 228, 0.2)',
                    width: 'fit-content',
                    minWidth: '120px',
                    ml: 'auto',
                    position: 'relative',
                  }}
                >
                  <Typography variant="body1" sx={{ textAlign: 'left', lineHeight: 1.4 }}>
                    {group.userMessage.message}
                  </Typography>
                </Paper>
                <Typography
                  variant="caption"
                  color="text.disabled"
                  sx={{
                    display: 'block',
                    textAlign: 'right',
                    mt: 0.5,
                    fontSize: '0.75rem',
                  }}
                >
                  {new Date(group.userMessage.timestamp).toLocaleTimeString()}
                </Typography>
              </Box>
            </Box>

            {/* Agent Responses as Accordions */}
            <Stack spacing={2}>
              {group.responses.map((msg) => (
                <Accordion
                  key={msg.id}
                  expanded={expandedAccordion === msg.id}
                  onChange={() => handleAccordionChange(msg.id)}
                  sx={{
                    boxShadow: 'none',
                    border: '1px solid #E0E0E0',
                    borderRadius: '12px !important',
                    backgroundColor: '#F8F9FA',
                    '&:before': {
                      display: 'none',
                    },
                    '&.Mui-expanded': {
                      margin: 0,
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                    sx={{
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      minHeight: '48px',
                      '&.Mui-expanded': {
                        borderBottomLeftRadius: 0,
                        borderBottomRightRadius: 0,
                      },
                      '& .MuiAccordionSummary-content': {
                        alignItems: 'center',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          backgroundColor: '#9C6FE4',
                          color: 'white',
                          fontSize: '14px',
                          fontWeight: 'bold',
                        }}
                      >
                        A
                      </Avatar>
                      <Typography variant="body2" fontWeight={500}>
                        {msg.source === 'Tool'
                          ? msg.event === 'tool_call_request'
                            ? 'Tool Request'
                            : msg.event === 'tool_call_response'
                              ? 'Tool Response'
                              : 'Tool'
                          : msg.event === 'task_result'
                            ? 'Task Result'
                            : msg.event === 'complete'
                              ? 'Task Completed'
                              : msg.event === 'error'
                                ? 'Task Failed'
                                : 'Agent Response'}
                      </Typography>
                      <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="caption" color="text.disabled">
                          {new Date(msg.timestamp).toLocaleTimeString()}
                        </Typography>
                        {msg.status === 'completed' && (
                          <Chip size="small" color="success" label="Completed" />
                        )}
                        {msg.isError && <Chip size="small" color="error" label="Failed" />}
                      </Box>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails sx={{ pt: 0 }}>
                    {msg?.event?.startsWith('tool_call') ? (
                      <Box>
                        {msg.message.split('\n\n').map((block, idx) => (
                          <Card
                            key={idx}
                            variant="outlined"
                            sx={{
                              p: 2,
                              mb: 1,
                              backgroundColor:
                                msg.event === 'tool_call_response' && msg.isError
                                  ? '#ffe6e6'
                                  : '#f7f7f7',
                              borderRadius: 2,
                              borderColor:
                                msg.event === 'tool_call_response' && msg.isError
                                  ? 'error.main'
                                  : 'grey.300',
                            }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box sx={{ flex: 1 }}>
                                {block.split('\n').map((line, i) => (
                                  <Typography key={i} variant="body2" whiteSpace="pre-wrap">
                                    {line}
                                  </Typography>
                                ))}
                              </Box>
                              {msg.event === 'tool_call_response' && (
                                <Box>
                                  {msg.isError ? (
                                    <Iconify
                                      icon="dashicons:no"
                                      style={{ color: '#ff473e', width: '16px', height: '16px' }}
                                    />
                                  ) : (
                                    <Iconify
                                      icon="el:ok"
                                      style={{
                                        color: '#167548',
                                        width: '16px',
                                        height: '16px',
                                      }}
                                    />
                                  )}
                                </Box>
                              )}
                            </Box>
                          </Card>
                        ))}
                      </Box>
                    ) : (
                      <Box>
                        <Typography variant="body2" whiteSpace="pre-wrap" sx={{ mb: 2 }}>
                          {msg.event === 'error' ? msg.message || 'An error occurred' : msg.message}
                        </Typography>

                        {/* Task Complete */}
                        {msg.event === 'complete' && !errorMsg && (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              p: 2,
                              backgroundColor: '#E8F5E8',
                              borderRadius: 1,
                              border: '1px solid #4CAF50',
                            }}
                          >
                            <Iconify
                              icon="el:ok"
                              style={{ color: '#167548', width: '20px', height: '20px' }}
                            />
                            <Typography sx={{ color: '#167548' }} variant="body2" fontWeight={500}>
                              Task Completed Successfully
                            </Typography>
                          </Box>
                        )}

                        {/* Task Error */}
                        {msg.event === 'error' && (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              p: 2,
                              backgroundColor: '#FFEBEE',
                              borderRadius: 1,
                              border: '1px solid #F44336',
                            }}
                          >
                            <Iconify
                              icon="dashicons:no"
                              style={{ color: '#ff473e', width: '20px', height: '20px' }}
                            />
                            <Typography sx={{ color: '#ff473e' }} variant="body2" fontWeight={500}>
                              Task Failed
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    )}
                  </AccordionDetails>
                </Accordion>
              ))}
            </Stack>
          </Box>
        ))}
      </Stack>
    </>
  );
};

export default ChatMessagesEvents;
