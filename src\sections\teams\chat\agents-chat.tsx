import {
  Box,
  Card,
  Chip,
  CircularProgress,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { AppButton } from 'src/components/common';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import ChatMessagesEvents from './chat-messages-events';
import NewChatIconText from './new-chat-icon-text';
import useTeamTemplatesChat from './use-agents-chat';

// ----------------------------------------------------------------------

// Mock data

const TeamTemplatesChat = () => {
  const {
    theme,
    message,
    setMessage,
    isNewChat,
    setIsNewChat,
    sidebarOpen,
    setSidebarOpen,
    handleSendMessage,
    handleKeyPress,
    toggleSidebar,
    chatResponse,
    tasksResponse,
    isPendingTask,
    chatMessages,
    setChatMessages,
    startStreaming,
    setStartStreaming,
    navigate,
    setChatId,
    isLoadingTasks,
    error,
    addNewChat,
    isLoadingChat,
    agentName,
    isLoadingChats,
    handleDeleteChat,
    openConfirmationDelete,
    setOpenConfirmationDelete,
    handleConfirmDeleteChat,
    isPendingDeleteChat,
    handleEnhanceTask,
    isPendingEnhanceChat,
  } = useTeamTemplatesChat();

  return (
    <Box
      sx={{
        display: 'flex',
        height: '96vh',
      }}
    >
      {/* Left Sidebar - Chat History */}
      {sidebarOpen && (
        <Box
          sx={{
            width: '22%',
            bgcolor: '#FFFFFF',
            borderRight: `1px solid #E0E0E0`,
            display: 'flex',
            flexDirection: 'column',
            mx: '20px',
            borderRadius: '24px',
          }}
        >
          {/* Header */}
          <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}
            >
              <Typography variant="h6" fontWeight={600}>
                Chats History
              </Typography>
              <AppButton
                fullWidth={false}
                variant="contained"
                size="small"
                onClick={addNewChat}
                sx={{
                  bgcolor: '#9C6FE4',
                  '&:hover': { bgcolor: '#8B5CF6' },
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  px: 2,
                }}
                label="New Chat"
                isLoading={isLoadingChat}
              />
            </Box>
          </Box>

          {/* Chat History List */}

          {isLoadingChats ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
              {chatResponse &&
                chatResponse?.chats?.length > 0 &&
                chatResponse?.chats?.map((item) => (
                  <Card
                    key={item?.id}
                    sx={{
                      p: 2.5,
                      mb: 2,
                      cursor: 'pointer',
                      border: `1px solid #E0E0E0`,
                      borderRadius: 2,
                      bgcolor: item?.status === 'active' ? '#F8F9FA' : '#FFFFFF',
                      '&:hover': {
                        bgcolor: '#F0F0F0',
                        borderColor: '#9C6FE4',
                      },
                    }}
                    onClick={() => {
                      setChatMessages([]);
                      setStartStreaming(false);
                      setChatId(item?.id);
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                      }}
                    >
                      <Box sx={{ flex: 1, cursor: 'pointer' }}>
                        <Typography
                          variant="h5"
                          fontWeight={600}
                          sx={{ mb: 1.5, color: '#333', fontSize: '0.875rem' }}
                        >
                          {item.title}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#999', fontSize: '0.75rem' }}>
                          {item.createdAt}
                        </Typography>
                      </Box>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteChat(item?.id);
                        }}
                        sx={{
                          color: '#999',
                          '&:hover': {
                            color: '#f24848',
                            bgcolor: 'rgba(242, 72, 72, 0.1)',
                          },
                        }}
                      >
                        <Iconify icon="material-symbols:delete-outline" width={16} height={16} />
                      </IconButton>
                    </Box>
                  </Card>
                ))}
            </Box>
          )}
        </Box>
      )}

      {/* Main Chat Area */}
      <Card
        sx={{ padding: '24px', width: '100%', flex: 1, display: 'flex', flexDirection: 'column' }}
      >
        {/* Chat Header */}
        {isLoadingTasks ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Box
              sx={{
                p: 2,
                borderBottom: `1px solid #E0E0E0`,
                bgcolor: '#FFFFFF',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <IconButton size="small" onClick={toggleSidebar}>
                  <Iconify icon="eva:menu-2-fill" />
                </IconButton>
                <IconButton onClick={() => navigate(paths.dashboard.teams.templates)} size="small">
                  <Iconify icon="eva:arrow-back-fill" />
                </IconButton>
                <Typography variant="h6" fontWeight={600} color="#333">
                  {agentName}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton onClick={() => navigate(paths.dashboard.root)} size="small">
                  <Iconify icon="eva:more-vertical-fill" />
                </IconButton>
                <IconButton size="small" onClick={() => navigate(paths.dashboard.root)}>
                  <Iconify icon="eva:arrow-forward-fill" />
                </IconButton>
              </Box>
            </Box>
            {startStreaming && (
              <Box sx={{ p: '24px', overflowY: 'auto', flex: 1 }}>
                <AppButton
                  disabled={isPendingTask}
                  fullWidth={false}
                  label="back"
                  variant="outlined"
                  onClick={() => {
                    setChatMessages([]);
                    setStartStreaming(false);
                  }}
                  startIcon={<Iconify icon="material-symbols:arrow-back-rounded" />}
                  sx={{ background: 'white', color: 'black' }}
                />
                <Box sx={{ overflow: 'auto' }}>
                  <ChatMessagesEvents
                    chatMessages={chatMessages}
                    message={error ? (error as { message: string })?.message : ''}
                  />
                </Box>
                {isPendingTask ? (
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      gap: '5px',
                    }}
                  >
                    <CircularProgress />
                    <Typography>generating response....</Typography>
                  </Box>
                ) : (
                  <></>
                )}
              </Box>
            )}

            {/* Chat Content */}
            {!startStreaming && (
              <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
                {isNewChat && tasksResponse?.tasks?.length === 0 ? (
                  // New Chat Empty State
                  <NewChatIconText />
                ) : // Chat History with Tasks
                tasksResponse && tasksResponse?.tasks?.length > 0 ? (
                  tasksResponse?.tasks?.map((task) => (
                    <Box key={task.id} sx={{ mb: 3 }}>
                      {/* Task Card */}
                      <Card
                        sx={{
                          p: 3,
                          border: `1px solid #E0E0E0`,
                          borderRadius: 2,
                          bgcolor: '#FFFFFF',
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'flex-start',
                          }}
                        >
                          <Box sx={{ flex: 1 }}>
                            <Typography
                              variant="body1"
                              fontWeight={500}
                              sx={{ mb: 2, color: '#333' }}
                            >
                              {task.content}
                            </Typography>
                            <Chip
                              label={task?.metadata?.taskResult?.status}
                              size="medium"
                              variant="filled"
                              color={
                                task?.metadata?.taskResult?.status === 'error' ? 'error' : 'success'
                              }
                            />
                          </Box>
                          {/* <Button
                      variant="outlined"
                      size="small"
                      sx={{
                        textTransform: 'none',
                        borderColor: '#E0E0E0',
                        color: '#666',
                        '&:hover': {
                          borderColor: '#9C6FE4',
                          color: '#9C6FE4',
                        },
                      }}
                    >
                      View Task Details
                    </Button> */}
                        </Box>
                      </Card>
                    </Box>
                  ))
                ) : (
                  <NewChatIconText />
                )}
              </Box>
            )}

            {/* Message Input */}
            <Box>
              {isPendingTask ? (
                <></>
              ) : (
                <Box sx={{}}>
                  <Box sx={{ flex: 1 }}>
                    <></>
                  </Box>
                  <TextField
                    multiline
                    fullWidth
                    placeholder="Ask me anything"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleKeyPress}
                    InputProps={{
                      startAdornment: (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                          <InputAdornment position="start">
                            <Iconify icon="mdi:plus-circle" sx={{ color: '#999' }} />
                          </InputAdornment>
                          <Iconify icon="pepicons-pencil:line-y" sx={{ color: '#8c8484' }} />
                        </Box>
                      ),
                      endAdornment: (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: '2px' }}>
                          <Iconify icon="pepicons-pencil:line-y" sx={{ color: '#8c8484' }} />
                          {isPendingEnhanceChat ? (
                            <CircularProgress />
                          ) : (
                            <IconButton disabled={isPendingTask || !message}>
                              <Tooltip title="enhance your prompt">
                                <Iconify
                                  icon="mdi:thunder"
                                  width="48"
                                  height="48"
                                  sx={{ cursor: 'pointer' }}
                                  onClick={handleEnhanceTask}
                                />
                              </Tooltip>
                            </IconButton>
                          )}
                          <InputAdornment position="end">
                            <IconButton
                              onClick={handleSendMessage}
                              disabled={isPendingTask || !message}
                              sx={{
                                bgcolor: message.trim() ? '#9C6FE4' : 'transparent',
                                color: message.trim() ? 'white' : '#999',
                                '&:hover': {
                                  bgcolor: message.trim() ? '#8B5CF6' : '#F5F5F5',
                                },
                              }}
                            >
                              <Tooltip title="send task">
                                <Iconify icon="maki:arrow" />
                              </Tooltip>
                            </IconButton>
                          </InputAdornment>
                        </Box>
                      ),
                      sx: {
                        borderRadius: 3,
                        bgcolor: '#F8F9FA',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #E0E0E0',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #9C6FE4',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          border: '2px solid #9C6FE4',
                        },
                      },
                    }}
                  />
                </Box>
              )}
            </Box>
          </>
        )}
      </Card>

      {/* Delete Chat Confirmation Dialog */}
      <ConfirmDialog
        open={openConfirmationDelete}
        onClose={() => setOpenConfirmationDelete(false)}
        title="Delete Chat"
        content="Are you sure you want to delete this chat? This action cannot be undone."
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton
              label="Cancel"
              variant="outlined"
              onClick={() => setOpenConfirmationDelete(false)}
            />
            <AppButton
              isLoading={isPendingDeleteChat}
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDeleteChat}
            />
          </Box>
        }
      />
    </Box>
  );
};

export default TeamTemplatesChat;
