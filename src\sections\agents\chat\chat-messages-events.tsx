import { Alert, Box, Paper, Typography, Chip, Card } from '@mui/material';
import { useEffect } from 'react';
import { enqueueSnackbar } from 'notistack';
import { Iconify } from 'src/components/iconify';
import { StreamedMessage } from './use-agents-chat';

type ChatMessagesType = {
  chatMessages: StreamedMessage[];
  message?: string;
};

const ChatMessagesEvents = ({ chatMessages, message }: ChatMessagesType) => {
  const currentLng = localStorage.getItem('i18nextLng') || 'en';
  const errorMsg = chatMessages?.find((msg) => msg.event === 'error');

  useEffect(() => {
    if (errorMsg) {
      enqueueSnackbar({
        variant: 'error',
        message: errorMsg?.message,
        anchorOrigin: {
          vertical: 'top',
          horizontal: currentLng === 'en' ? 'right' : 'left',
        },
      });
    }
  }, [chatMessages]);

  console.log('Message', message);

  return (
    <>
      {message && message.length > 0 && (
        <Alert sx={{ mt: '20px' }} variant="filled" color="error">
          <Typography sx={{ color: 'white' }}>{message}</Typography>
        </Alert>
      )}

      {chatMessages.map((msg) => (
        <Box key={msg.id} display="flex" justifyContent="flex-start" mb={2} mt={3}>
          <Paper
            elevation={3}
            sx={{
              px: 2,
              py: 1.5,
              width: '100%',
              borderRadius: 3,
              border: '1px solid #E0DFE2',
              backgroundColor: (theme) =>
                msg.source === 'user'
                  ? theme.palette.primary.light
                  : theme.palette.background.default,
              color: (theme) =>
                msg.source === 'user'
                  ? theme.palette.background.default
                  : theme.palette.text.primary,
            }}
          >
            {/* Message Header */}
            <Typography
              variant="body2"
              fontWeight="bold"
              gutterBottom
              sx={{ textAlign: msg.source === 'user' ? 'right' : 'left' }}
            >
              {msg.source === 'user'
                ? 'You'
                : msg.source === 'Tool'
                  ? msg.event === 'tool_call_request'
                    ? 'Tool Request'
                    : msg.event === 'tool_call_response'
                      ? 'Tool Response'
                      : 'Tool'
                  : msg.event === 'task_result'
                    ? 'Task Result'
                    : msg.event === 'complete'
                      ? ''
                      : 'Agent'}
            </Typography>

            {/* Tool Call UI */}
            {msg?.event?.startsWith('tool_call') ? (
              <Box mt={1}>
                {msg.message.split('\n\n').map((block, idx) => (
                  <Card
                    key={idx}
                    variant="outlined"
                    sx={{
                      p: 2,
                      mb: 1,
                      backgroundColor:
                        msg.event === 'tool_call_response' && msg.isError ? '#ffe6e6' : '#f7f7f7',
                      borderRadius: 2,
                      borderColor:
                        msg.event === 'tool_call_response' && msg.isError
                          ? 'error.main'
                          : 'grey.300',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                      <Box>
                        {block.split('\n').map((line, i) => (
                          <Typography key={i} variant="body1" whiteSpace="pre-wrap">
                            {line}
                          </Typography>
                        ))}
                      </Box>
                      <Box>
                        {msg.event === 'tool_call_response' && (
                          <Box sx={{}}>
                            {msg.isError ? (
                              <Iconify
                                icon="dashicons:no"
                                style={{ color: '#ff473e', width: '12px', height: '24px' }}
                              />
                            ) : (
                              <Iconify
                                icon="el:ok"
                                style={{
                                  color: '#167548',
                                  width: '12px',
                                  height: '24px',
                                }}
                              />
                            )}
                          </Box>
                        )}
                      </Box>
                    </Box>
                  </Card>
                ))}
              </Box>
            ) : (
              // Default Text Message
              <Typography
                variant="body1"
                whiteSpace="pre-wrap"
                sx={{ textAlign: msg.source === 'user' ? 'right' : 'left' }}
              >
                {msg.event === 'error' ? '' : msg.message}
              </Typography>
            )}

            {/* Status Indicators */}
            {msg.status === 'completed' && (
              <Chip sx={{ mt: '20px' }} variant="soft" color="success" label="Completed" />
            )}

            {/* Task Complete */}
            {msg.event === 'complete' && !errorMsg && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '5px',
                  padding: '20px',
                }}
              >
                <Iconify icon="el:ok" style={{ color: '#167548', width: '24px', height: '48px' }} />
                <Typography sx={{ color: '#167548' }} variant="h6">
                  Task Completed
                </Typography>
              </Box>
            )}

            {/* Task Error */}
            {msg.event === 'error' && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '5px',
                  padding: '20px',
                }}
              >
                <Iconify
                  icon="dashicons:no"
                  style={{ color: '#ff473e', width: '24px', height: '48px' }}
                />
                <Typography sx={{ color: '#ff473e' }} variant="h6">
                  Task Failed
                </Typography>
              </Box>
            )}
          </Paper>
        </Box>
      ))}
    </>
  );
};

export default ChatMessagesEvents;
